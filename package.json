{"name": "school-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fontsource/inter": "^5.1.1", "@heroicons/react": "^2.2.0", "jspdf": "^2.5.1", "lucide-react": "^0.471.0", "motion": "^11.16.4", "next": "15.1.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "swr": "^2.3.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}