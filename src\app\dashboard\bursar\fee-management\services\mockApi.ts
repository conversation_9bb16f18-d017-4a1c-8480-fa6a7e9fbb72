// app/fee-management/services/mockApi.ts

import { Student, Payment } from "../types";

const mockStudents: Student[] = [
  {
    id: "ST001",
    name: "<PERSON>",
    class: "10A",
    expectedFees: 56500,
    paidFees: 40000,
    lastPaymentDate: "2024-02-15",
    status: "Partial",
    email: "<EMAIL>",
    parentName: "<PERSON>",
    parentPhone: "+237612345678",
    admissionNumber: "ADM2023001",
    balance: 16500,
    parentContacts: [
      {
        name: "<PERSON>",
        phone: "+237612345678",
        email: "<EMAIL>",
      },
    ],
  },
  {
    id: "ST002",
    name: "<PERSON>",
    class: "10A",
    expectedFees: 56500,
    paidFees: 56500,
    lastPaymentDate: "2024-02-10",
    status: "Paid",
    email: "<EMAIL>",
    parentName: "<PERSON>",
    parentPhone: "+237612345679",
    admissionNumber: "ADM2023002",
    balance: 0,
    parentContacts: [
      {
        name: "<PERSON>",
        phone: "+237612345679",
        email: "<EMAIL>",
      },
    ],
  },
];

export const fetchStudents = async (): Promise<Student[]> => {
  // Simulate a delay to mimic network latency
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return mockStudents;
};

export const addStudent = async (student: Student): Promise<Student> => {
  // Simulate a delay to mimic network latency
  await new Promise((resolve) => setTimeout(resolve, 1000));
  mockStudents.push(student);
  return student;
};

export const recordPayment = async (payment: Payment): Promise<Payment> => {
  // Simulate a delay to mimic network latency
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return payment;
};