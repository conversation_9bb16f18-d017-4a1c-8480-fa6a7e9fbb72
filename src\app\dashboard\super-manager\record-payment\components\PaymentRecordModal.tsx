'use client';

import { XMarkIcon } from '@heroicons/react/24/outline';
import { RecordPaymentForm } from './RecordPaymentForm';

type Student = {
    id: number;
    name: string;
    matricule?: string;
    classId?: number;
    className?: string;
};

type Bank = {
    id: string;
    name: string;
    code: string;
};

interface PaymentRecordModalProps {
    isOpen: boolean;
    onClose: () => void;
    students: Student[];
    banks: Bank[];
    onSubmit: (data: {
        studentId: number;
        bank: string;
        datePaid: string;
        amountPaid: number;
        paymentReference?: string;
    }) => void;
    isLoading: boolean;
    selectedStudent?: Student | null;
}

export function PaymentRecordModal({
    isOpen,
    onClose,
    students,
    banks,
    onSubmit,
    isLoading,
    selectedStudent
}: PaymentRecordModalProps) {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative mx-auto border w-full max-w-2xl shadow-lg rounded-md bg-white">
                {/* Modal Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                    <div>
                        <h3 className="text-lg font-medium leading-6 text-gray-900">
                            Record Payment
                        </h3>
                        <p className="mt-1 text-sm text-gray-500">
                            Record a new fee payment from a student
                        </p>
                    </div>
                    <button
                        onClick={onClose}
                        disabled={isLoading}
                        className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
                    >
                        <span className="sr-only">Close</span>
                        <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                </div>

                {/* Modal Body */}
                <div className="p-6">
                    <RecordPaymentForm
                        students={students}
                        banks={banks}
                        onSubmit={onSubmit}
                        isLoading={isLoading}
                        selectedStudent={selectedStudent}
                    />
                </div>

                {/* Modal Footer - Cancel Button */}
                <div className="flex justify-start px-6 pb-6">
                    <button
                        type="button"
                        onClick={onClose}
                        disabled={isLoading}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    );
}
