'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon, CalendarIcon, BanknotesIcon } from '@heroicons/react/24/outline';

type Student = {
    id: number;
    name: string;
    matricule?: string;
    classId?: number;
    className?: string;
};

type Bank = {
    id: string;
    name: string;
    code: string;
};

type PaymentFormData = {
    studentId: number | null;
    bank: string;
    datePaid: string;
    amountPaid: number | '';
    paymentReference: string;
};

interface RecordPaymentFormProps {
    students: Student[];
    banks: Bank[];
    onSubmit: (data: {
        studentId: number;
        bank: string;
        datePaid: string;
        amountPaid: number;
        paymentReference?: string;
    }) => void;
    isLoading: boolean;
    selectedStudent?: Student | null;
}

export function RecordPaymentForm({ 
    students, 
    banks, 
    onSubmit, 
    isLoading,
    selectedStudent 
}: RecordPaymentFormProps) {
    const [formData, setFormData] = useState<PaymentFormData>({
        studentId: selectedStudent?.id || null,
        bank: '',
        datePaid: new Date().toISOString().split('T')[0], // Today's date
        amountPaid: '',
        paymentReference: ''
    });

    const [studentSearch, setStudentSearch] = useState(selectedStudent?.name || '');
    const [showStudentDropdown, setShowStudentDropdown] = useState(false);
    const [errors, setErrors] = useState<Record<string, string>>({});

    // Filter students based on search
    const filteredStudents = students.filter(student =>
        student.name.toLowerCase().includes(studentSearch.toLowerCase()) ||
        student.matricule?.toLowerCase().includes(studentSearch.toLowerCase())
    );

    // Handle student selection
    const handleStudentSelect = (student: Student) => {
        setFormData(prev => ({ ...prev, studentId: student.id }));
        setStudentSearch(student.name);
        setShowStudentDropdown(false);
        setErrors(prev => ({ ...prev, studentId: '' }));
    };

    // Handle input changes
    const handleInputChange = (field: keyof PaymentFormData, value: string | number) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        setErrors(prev => ({ ...prev, [field]: '' }));
    };

    // Validate form
    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.studentId) {
            newErrors.studentId = 'Please select a student';
        }

        if (!formData.bank) {
            newErrors.bank = 'Please select a bank';
        }

        if (!formData.datePaid) {
            newErrors.datePaid = 'Please select a payment date';
        }

        if (!formData.amountPaid || formData.amountPaid <= 0) {
            newErrors.amountPaid = 'Please enter a valid amount';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        onSubmit({
            studentId: formData.studentId!,
            bank: formData.bank,
            datePaid: formData.datePaid,
            amountPaid: Number(formData.amountPaid),
            paymentReference: formData.paymentReference || undefined
        });
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            {/* Student Selection */}
            <div className="relative">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Student *
                </label>
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                        type="text"
                        value={studentSearch}
                        onChange={(e) => {
                            setStudentSearch(e.target.value);
                            setShowStudentDropdown(true);
                            if (!e.target.value) {
                                setFormData(prev => ({ ...prev, studentId: null }));
                            }
                        }}
                        onFocus={() => setShowStudentDropdown(true)}
                        className={`block w-full pl-10 pr-3 py-2 border rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                            errors.studentId ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Search for a student..."
                        disabled={isLoading}
                    />
                </div>
                
                {/* Student Dropdown */}
                {showStudentDropdown && studentSearch && (
                    <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                        {filteredStudents.length > 0 ? (
                            filteredStudents.map((student) => (
                                <div
                                    key={student.id}
                                    onClick={() => handleStudentSelect(student)}
                                    className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50"
                                >
                                    <div className="flex items-center">
                                        <span className="font-medium text-gray-900 block truncate">
                                            {student.name}
                                        </span>
                                    </div>
                                    <div className="text-sm text-gray-500">
                                        {student.matricule && (
                                            <span className="mr-2">Matricule: {student.matricule}</span>
                                        )}
                                        {student.className && (
                                            <span>Class: {student.className}</span>
                                        )}
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div className="py-2 pl-3 pr-9 text-gray-500">
                                No students found
                            </div>
                        )}
                    </div>
                )}
                
                {errors.studentId && (
                    <p className="mt-1 text-sm text-red-600">{errors.studentId}</p>
                )}
            </div>

            {/* Bank Selection */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bank *
                </label>
                <select
                    value={formData.bank}
                    onChange={(e) => handleInputChange('bank', e.target.value)}
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        errors.bank ? 'border-red-300' : 'border-gray-300'
                    }`}
                    disabled={isLoading}
                >
                    <option value="">Select a bank</option>
                    {banks.map((bank) => (
                        <option key={bank.id} value={bank.id}>
                            {bank.name}
                        </option>
                    ))}
                </select>
                {errors.bank && (
                    <p className="mt-1 text-sm text-red-600">{errors.bank}</p>
                )}
            </div>

            {/* Date Paid */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date Paid *
                </label>
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <CalendarIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                        type="date"
                        value={formData.datePaid}
                        onChange={(e) => handleInputChange('datePaid', e.target.value)}
                        max={new Date().toISOString().split('T')[0]} // Can't select future dates
                        className={`block w-full pl-10 pr-3 py-2 border rounded-md leading-5 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                            errors.datePaid ? 'border-red-300' : 'border-gray-300'
                        }`}
                        disabled={isLoading}
                    />
                </div>
                {errors.datePaid && (
                    <p className="mt-1 text-sm text-red-600">{errors.datePaid}</p>
                )}
            </div>

            {/* Amount Paid */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Amount Paid (FCFA) *
                </label>
                <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <BanknotesIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                        type="number"
                        value={formData.amountPaid}
                        onChange={(e) => handleInputChange('amountPaid', e.target.value ? Number(e.target.value) : '')}
                        min="0"
                        step="1"
                        className={`block w-full pl-10 pr-3 py-2 border rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                            errors.amountPaid ? 'border-red-300' : 'border-gray-300'
                        }`}
                        placeholder="Enter amount in FCFA"
                        disabled={isLoading}
                    />
                </div>
                {errors.amountPaid && (
                    <p className="mt-1 text-sm text-red-600">{errors.amountPaid}</p>
                )}
            </div>

            {/* Payment Reference (Optional) */}
            <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Reference
                    <span className="text-gray-400 text-sm ml-1">(Optional)</span>
                </label>
                <input
                    type="text"
                    value={formData.paymentReference}
                    onChange={(e) => handleInputChange('paymentReference', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Bank transaction reference or receipt number"
                    disabled={isLoading}
                />
            </div>

            {/* Auto-generated Matricule Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex">
                    <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">
                            Payment Matricule
                        </h3>
                        <div className="mt-1 text-sm text-blue-700">
                            A unique payment matricule will be automatically generated when you submit this form.
                        </div>
                    </div>
                </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                    type="submit"
                    disabled={isLoading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {isLoading ? (
                        <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Recording...
                        </>
                    ) : (
                        'Record Payment'
                    )}
                </button>
            </div>

            {/* Click outside to close dropdown */}
            {showStudentDropdown && (
                <div 
                    className="fixed inset-0 z-0" 
                    onClick={() => setShowStudentDropdown(false)}
                />
            )}
        </form>
    );
}
