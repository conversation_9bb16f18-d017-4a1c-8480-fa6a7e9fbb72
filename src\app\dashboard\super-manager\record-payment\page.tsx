'use client';

import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { PlusIcon, MagnifyingGlassIcon, BanknotesIcon } from '@heroicons/react/24/outline';
import { RecordPaymentForm } from './components/RecordPaymentForm';
import { PaymentRecordModal } from './components/PaymentRecordModal';
import apiService from '../../../../lib/apiService';

// --- Types ---
type Student = {
    id: number;
    name: string;
    matricule?: string;
    classId?: number;
    className?: string;
};

type PaymentRecord = {
    id: number;
    student: Student;
    bank: string;
    datePaid: string;
    amountPaid: number;
    paymentMatricule: string;
    paymentReference?: string;
    createdAt: string;
};

type Bank = {
    id: string;
    name: string;
    code: string;
};

const BANKS: Bank[] = [
    { id: 'express_union', name: 'Express Union', code: 'EU' },
    { id: 'cca', name: 'CCA Bank', code: 'CCA' },
    { id: '3dc', name: '3DC Bank', code: '3DC' }
];

export default function RecordPaymentPage() {
    const [students, setStudents] = useState<Student[]>([]);
    const [paymentRecords, setPaymentRecords] = useState<PaymentRecord[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [isFormModalOpen, setIsFormModalOpen] = useState(false);
    const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

    // API endpoints
    const STUDENTS_ENDPOINT = '/students';
    const PAYMENTS_ENDPOINT = '/payments';

    // Fetch students for selection
    const fetchStudents = async () => {
        try {
            const result = await apiService.get<{ data: any[] }>(STUDENTS_ENDPOINT);
            const mappedStudents = result.data?.map((student: any) => ({
                id: student.id,
                name: student.name,
                matricule: student.matricule,
                classId: student.class?.id,
                className: student.class?.name
            })) || [];
            setStudents(mappedStudents);
        } catch (error: any) {
            console.error("Failed to fetch students:", error);
            if (error.message !== 'Unauthorized') {
                toast.error(`Failed to load students: ${error.message}`);
            }
        }
    };

    // Fetch payment records
    const fetchPaymentRecords = async () => {
        setIsLoading(true);
        try {
            const result = await apiService.get<{ data: any[] }>(PAYMENTS_ENDPOINT);
            const mappedRecords = result.data?.map((record: any) => ({
                id: record.id,
                student: {
                    id: record.student?.id,
                    name: record.student?.name,
                    matricule: record.student?.matricule,
                    classId: record.student?.class?.id,
                    className: record.student?.class?.name
                },
                bank: record.bank,
                datePaid: record.date_paid,
                amountPaid: record.amount_paid,
                paymentMatricule: record.payment_matricule,
                paymentReference: record.payment_reference,
                createdAt: record.created_at
            })) || [];
            setPaymentRecords(mappedRecords);
        } catch (error: any) {
            console.error("Failed to fetch payment records:", error);
            if (error.message !== 'Unauthorized') {
                toast.error(`Failed to load payment records: ${error.message}`);
            }
        } finally {
            setIsLoading(false);
        }
    };

    // Generate payment matricule
    const generatePaymentMatricule = (): string => {
        const timestamp = Date.now().toString().slice(-6);
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `PAY${timestamp}${random}`;
    };

    // Handle payment submission
    const handlePaymentSubmit = async (paymentData: {
        studentId: number;
        bank: string;
        datePaid: string;
        amountPaid: number;
        paymentReference?: string;
    }) => {
        setIsLoading(true);
        const paymentMatricule = generatePaymentMatricule();
        
        const payload = {
            student_id: paymentData.studentId,
            bank: paymentData.bank,
            date_paid: paymentData.datePaid,
            amount_paid: paymentData.amountPaid,
            payment_matricule: paymentMatricule,
            payment_reference: paymentData.paymentReference || null
        };

        try {
            await apiService.post(PAYMENTS_ENDPOINT, payload);
            toast.success(`Payment recorded successfully! Matricule: ${paymentMatricule}`);
            setIsFormModalOpen(false);
            setSelectedStudent(null);
            fetchPaymentRecords(); // Refresh the list
        } catch (error: any) {
            console.error("Payment recording error:", error);
            // apiService handles toast for errors
        } finally {
            setIsLoading(false);
        }
    };

    // Filter students based on search
    const filteredStudents = students.filter(student =>
        student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        student.matricule?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Filter payment records based on search
    const filteredPaymentRecords = paymentRecords.filter(record =>
        record.student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.student.matricule?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.paymentMatricule.toLowerCase().includes(searchTerm.toLowerCase())
    );

    useEffect(() => {
        fetchStudents();
        fetchPaymentRecords();
    }, []);

    // Format currency
    const formatCurrency = (amount: number): string => {
        return new Intl.NumberFormat('fr-CM', { 
            style: 'currency', 
            currency: 'FCFA', 
            minimumFractionDigits: 0 
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString: string): string => {
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="mb-6">
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">Record Payment</h1>
                    <p className="text-gray-600">Record student fee payments from various banks</p>
                </div>

                {/* Action Bar */}
                <div className="mb-6 flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
                    {/* Search */}
                    <div className="relative flex-1 max-w-md">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Search students or payment records..."
                        />
                    </div>

                    {/* Record Payment Button */}
                    <button
                        onClick={() => setIsFormModalOpen(true)}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                        disabled={isLoading}
                    >
                        <PlusIcon className="h-4 w-4 mr-2" />
                        Record Payment
                    </button>
                </div>

                {/* Payment Records Table */}
                <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-medium text-gray-900 flex items-center">
                            <BanknotesIcon className="h-5 w-5 mr-2 text-gray-400" />
                            Recent Payment Records
                        </h3>
                    </div>
                    
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Student
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Payment Matricule
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Bank
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Amount
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Date Paid
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Reference
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {isLoading && (
                                    <tr>
                                        <td colSpan={6} className="text-center py-4 text-gray-500 italic">
                                            Loading payment records...
                                        </td>
                                    </tr>
                                )}
                                {!isLoading && filteredPaymentRecords.length === 0 && (
                                    <tr>
                                        <td colSpan={6} className="text-center py-4 text-gray-500">
                                            No payment records found.
                                        </td>
                                    </tr>
                                )}
                                {filteredPaymentRecords.map((record) => (
                                    <tr key={record.id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                {record.student.name}
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {record.student.matricule || `ID: ${record.student.id}`}
                                                {record.student.className && (
                                                    <span className="ml-2 text-xs text-gray-400">
                                                        • {record.student.className}
                                                    </span>
                                                )}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">
                                                {record.paymentMatricule}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="text-sm text-gray-900">
                                                {BANKS.find(b => b.id === record.bank)?.name || record.bank}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="text-sm font-medium text-green-600">
                                                {formatCurrency(record.amountPaid)}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {formatDate(record.datePaid)}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {record.paymentReference || '-'}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            {/* Record Payment Modal */}
            {isFormModalOpen && (
                <PaymentRecordModal
                    isOpen={isFormModalOpen}
                    onClose={() => {
                        setIsFormModalOpen(false);
                        setSelectedStudent(null);
                    }}
                    students={filteredStudents}
                    banks={BANKS}
                    onSubmit={handlePaymentSubmit}
                    isLoading={isLoading}
                    selectedStudent={selectedStudent}
                />
            )}
        </div>
    );
}
